// Copyright(C) 2023 InfiniFlow, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gtest/gtest.h"
import base_test;
import stl;
import third_party;
import rcu_multimap;

using namespace infinity;

// Test value class with reference counting
class TestValue {
public:
    explicit TestValue(i32 val) : value_(val), ref_count_(1) {}
    
    void IncrementRef() { ++ref_count_; }
    void DecrementRef() { 
        if (--ref_count_ == 0) {
            delete this;
        }
    }
    
    i32 GetValue() const { return value_; }
    i32 GetRefCount() const { return ref_count_; }

private:
    i32 value_;
    std::atomic<i32> ref_count_;
};

// Test value class without reference counting (for testing different deallocators)
class SimpleTestValue {
public:
    explicit SimpleTestValue(i32 val) : value_(val) {}
    i32 GetValue() const { return value_; }

private:
    i32 value_;
};

class RcuMultiMapTest : public BaseTest {
public:
    RcuMultiMapTest() = default;
    ~RcuMultiMapTest() = default;

    void SetUp() override {
        BaseTest::SetUp();
    }

    void TearDown() override {
        BaseTest::TearDown();
    }

protected:
    // Helper function to create test values
    TestValue* CreateTestValue(i32 val) {
        return new TestValue(val);
    }
    
    SimpleTestValue* CreateSimpleTestValue(i32 val) {
        return new SimpleTestValue(val);
    }
    
    // Helper function to verify values in vector
    void VerifyValues(const Vector<TestValue*>& values, const Vector<i32>& expected) {
        EXPECT_EQ(values.size(), expected.size());
        Vector<i32> actual_values;
        for (const auto* val : values) {
            actual_values.push_back(val->GetValue());
        }
        std::sort(actual_values.begin(), actual_values.end());
        Vector<i32> sorted_expected = expected;
        std::sort(sorted_expected.begin(), sorted_expected.end());
        EXPECT_EQ(actual_values, sorted_expected);
    }
};

TEST_F(RcuMultiMapTest, TestBasicInsertAndGet) {
    RcuMultiMap<String, TestValue> map;
    
    // Insert some values
    auto* val1 = CreateTestValue(10);
    auto* val2 = CreateTestValue(20);
    auto* val3 = CreateTestValue(30);
    
    map.Insert("key1", val1);
    map.Insert("key2", val2);
    map.Insert("key1", val3); // Multiple values for same key
    
    // Test Get
    auto values1 = map.Get("key1");
    EXPECT_EQ(values1.size(), 2);
    VerifyValues(values1, {10, 30});
    
    auto values2 = map.Get("key2");
    EXPECT_EQ(values2.size(), 1);
    VerifyValues(values2, {20});
    
    // Test non-existent key
    auto values3 = map.Get("key3");
    EXPECT_EQ(values3.size(), 0);
    
    // Test size
    EXPECT_GT(map.size(), 0);
}

TEST_F(RcuMultiMapTest, TestLowercaseAliases) {
    RcuMultiMap<String, TestValue> map;
    
    auto* val1 = CreateTestValue(100);
    auto* val2 = CreateTestValue(200);
    
    // Test lowercase insert alias
    map.insert("test_key", val1);
    map.insert("test_key", val2);
    
    // Test lowercase get alias
    auto values = map.get("test_key");
    EXPECT_EQ(values.size(), 2);
    VerifyValues(values, {100, 200});
}

TEST_F(RcuMultiMapTest, TestDelete) {
    RcuMultiMap<String, TestValue> map;
    
    auto* val1 = CreateTestValue(10);
    auto* val2 = CreateTestValue(20);
    auto* val3 = CreateTestValue(30);
    
    map.Insert("key1", val1);
    map.Insert("key1", val2);
    map.Insert("key2", val3);
    
    // Delete specific value
    map.Delete("key1", val1);
    auto values1 = map.Get("key1");
    EXPECT_EQ(values1.size(), 1);
    VerifyValues(values1, {20});
    
    // Delete entire key
    map.Delete("key2");
    auto values2 = map.Get("key2");
    EXPECT_EQ(values2.size(), 0);
    
    // Test lowercase delete alias
    map.delete_key("key1");
    auto values3 = map.Get("key1");
    EXPECT_EQ(values3.size(), 0);
}

TEST_F(RcuMultiMapTest, TestEmplace) {
    RcuMultiMap<String, TestValue> map;
    
    // Test emplace functionality
    map.emplace("key1", 42);
    map.emplace("key1", 84);
    
    auto values = map.Get("key1");
    EXPECT_EQ(values.size(), 2);
    VerifyValues(values, {42, 84});
}

TEST_F(RcuMultiMapTest, TestBounds) {
    RcuMultiMap<String, TestValue> map;
    
    auto* val1 = CreateTestValue(10);
    auto* val2 = CreateTestValue(20);
    auto* val3 = CreateTestValue(30);
    
    map.Insert("apple", val1);
    map.Insert("banana", val2);
    map.Insert("cherry", val3);
    
    // Test lower_bound
    auto lower_results = map.lower_bound("banana");
    EXPECT_GE(lower_results.size(), 1);
    
    // Test upper_bound
    auto upper_results = map.upper_bound("banana");
    EXPECT_GE(upper_results.size(), 0);
}

TEST_F(RcuMultiMapTest, TestGetAllValuesWithRef) {
    RcuMultiMap<String, TestValue> map;
    
    auto* val1 = CreateTestValue(10);
    auto* val2 = CreateTestValue(20);
    auto* val3 = CreateTestValue(30);
    
    map.Insert("key1", val1);
    map.Insert("key2", val2);
    map.Insert("key3", val3);
    
    Vector<TestValue*> all_values;
    map.GetAllValuesWithRef(all_values);
    
    EXPECT_EQ(all_values.size(), 3);
    VerifyValues(all_values, {10, 20, 30});
    
    // Clean up references
    for (auto* val : all_values) {
        val->DecrementRef();
    }
}

TEST_F(RcuMultiMapTest, TestCustomDeallocator) {
    // Test with ValueDelete deallocator for SimpleTestValue
    RcuMultiMap<String, SimpleTestValue> map(ValueNoOp<SimpleTestValue>, ValueDelete<SimpleTestValue>);
    
    auto* val1 = CreateSimpleTestValue(100);
    auto* val2 = CreateSimpleTestValue(200);
    
    map.Insert("key1", val1);
    map.Insert("key2", val2);
    
    auto values1 = map.Get("key1");
    EXPECT_EQ(values1.size(), 1);
    EXPECT_EQ(values1[0]->GetValue(), 100);
    
    auto values2 = map.Get("key2");
    EXPECT_EQ(values2.size(), 1);
    EXPECT_EQ(values2[0]->GetValue(), 200);
    
    // Delete will call ValueDelete automatically
    map.Delete("key1");
    map.Delete("key2");
}

TEST_F(RcuMultiMapTest, TestGarbageCollection) {
    RcuMultiMap<String, TestValue> map;
    
    auto* val1 = CreateTestValue(10);
    auto* val2 = CreateTestValue(20);
    
    map.Insert("key1", val1);
    map.Insert("key2", val2);
    
    // Delete some entries
    map.Delete("key1");
    
    // Test garbage collection (should not crash)
    uint64_t current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    
    // Test checkGc alias
    map.checkGc(current_time - 1000); // GC entries older than 1 second
    
    // Verify remaining data is still accessible
    auto values = map.Get("key2");
    EXPECT_EQ(values.size(), 1);
    VerifyValues(values, {20});
}

TEST_F(RcuMultiMapTest, TestExpiredEntries) {
    RcuMultiMap<String, TestValue> map;
    
    auto* val1 = CreateTestValue(10);
    auto* val2 = CreateTestValue(20);
    
    map.Insert("key1", val1);
    map.Insert("key2", val2);
    
    // Access key1 to update its access time
    map.Get("key1", true);
    
    // Sleep briefly to create time difference
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    
    uint64_t current_time = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    
    Vector<String> expired_keys;
    map.CheckExpired(current_time + 1000, expired_keys); // Check for entries not accessed in last second
    
    // Should find some expired keys (exact behavior depends on implementation)
    // This test mainly ensures the method doesn't crash
    EXPECT_GE(expired_keys.size(), 0);
}

TEST_F(RcuMultiMapTest, TestGetWithRcuTime) {
    RcuMultiMap<String, TestValue> map;

    auto* val1 = CreateTestValue(10);
    auto* val2 = CreateTestValue(20);

    map.Insert("key1", val1);
    map.Insert("key2", val2);

    // Test GetWithRcuTime (should behave similarly to Get but with RCU semantics)
    auto values1 = map.GetWithRcuTime("key1");
    EXPECT_EQ(values1.size(), 1);
    VerifyValues(values1, {10});

    auto values2 = map.GetWithRcuTime("key2");
    EXPECT_EQ(values2.size(), 1);
    VerifyValues(values2, {20});

    // Test non-existent key
    auto values3 = map.GetWithRcuTime("key3");
    EXPECT_EQ(values3.size(), 0);
}

TEST_F(RcuMultiMapTest, TestMultipleValuesPerKey) {
    RcuMultiMap<i32, TestValue> map;

    // Insert multiple values for the same key
    for (i32 i = 0; i < 10; ++i) {
        auto* val = CreateTestValue(i * 10);
        map.Insert(42, val);
    }

    auto values = map.Get(42);
    EXPECT_EQ(values.size(), 10);

    Vector<i32> expected_values;
    for (i32 i = 0; i < 10; ++i) {
        expected_values.push_back(i * 10);
    }
    VerifyValues(values, expected_values);
}

TEST_F(RcuMultiMapTest, TestIntegerKeys) {
    RcuMultiMap<i32, TestValue> map;

    auto* val1 = CreateTestValue(100);
    auto* val2 = CreateTestValue(200);
    auto* val3 = CreateTestValue(300);

    map.Insert(1, val1);
    map.Insert(2, val2);
    map.Insert(3, val3);

    // Test retrieval
    auto values1 = map.Get(1);
    EXPECT_EQ(values1.size(), 1);
    VerifyValues(values1, {100});

    auto values2 = map.Get(2);
    EXPECT_EQ(values2.size(), 1);
    VerifyValues(values2, {200});

    auto values3 = map.Get(3);
    EXPECT_EQ(values3.size(), 1);
    VerifyValues(values3, {300});

    // Test bounds with integer keys
    auto lower_results = map.lower_bound(2);
    EXPECT_GE(lower_results.size(), 1);

    auto upper_results = map.upper_bound(2);
    EXPECT_GE(upper_results.size(), 0);
}

TEST_F(RcuMultiMapTest, TestEmptyMap) {
    RcuMultiMap<String, TestValue> map;

    // Test operations on empty map
    auto values = map.Get("nonexistent");
    EXPECT_EQ(values.size(), 0);

    Vector<TestValue*> all_values;
    map.GetAllValuesWithRef(all_values);
    EXPECT_EQ(all_values.size(), 0);

    // Delete on empty map should not crash
    map.Delete("nonexistent");

    // Bounds on empty map should not crash
    auto lower_results = map.lower_bound("test");
    EXPECT_EQ(lower_results.size(), 0);

    auto upper_results = map.upper_bound("test");
    EXPECT_EQ(upper_results.size(), 0);
}

TEST_F(RcuMultiMapTest, TestReferenceCountingBehavior) {
    RcuMultiMap<String, TestValue> map;

    auto* val = CreateTestValue(42);
    i32 initial_ref_count = val->GetRefCount();

    // Insert should increment reference count
    map.Insert("key1", val);

    // Get should increment reference count for returned values
    auto values = map.Get("key1");
    EXPECT_EQ(values.size(), 1);
    EXPECT_GT(values[0]->GetRefCount(), initial_ref_count);

    // Clean up the reference from Get
    for (auto* v : values) {
        v->DecrementRef();
    }

    // Delete should decrement reference count
    map.Delete("key1");

    // Original reference should still be valid
    EXPECT_EQ(val->GetRefCount(), initial_ref_count);
    val->DecrementRef(); // Clean up original reference
}

TEST_F(RcuMultiMapTest, TestStressInsertDelete) {
    RcuMultiMap<i32, TestValue> map;

    const i32 num_operations = 100;
    Vector<TestValue*> created_values;

    // Insert many values
    for (i32 i = 0; i < num_operations; ++i) {
        auto* val = CreateTestValue(i);
        created_values.push_back(val);
        map.Insert(i % 10, val); // Use 10 different keys
    }

    // Verify all values are accessible
    for (i32 key = 0; key < 10; ++key) {
        auto values = map.Get(key);
        EXPECT_GT(values.size(), 0);
    }

    // Delete half the keys
    for (i32 key = 0; key < 5; ++key) {
        map.Delete(key);
    }

    // Verify deleted keys are gone
    for (i32 key = 0; key < 5; ++key) {
        auto values = map.Get(key);
        EXPECT_EQ(values.size(), 0);
    }

    // Verify remaining keys are still accessible
    for (i32 key = 5; key < 10; ++key) {
        auto values = map.Get(key);
        EXPECT_GT(values.size(), 0);
    }
}

// Test with different value types
TEST_F(RcuMultiMapTest, TestDifferentValueTypes) {
    // Test with pointer to int
    RcuMultiMap<String, i32> int_map(ValueNoOp<i32>, ValueNoOp<i32>);

    i32 value1 = 42;
    i32 value2 = 84;

    int_map.Insert("key1", &value1);
    int_map.Insert("key2", &value2);

    auto values1 = int_map.Get("key1");
    EXPECT_EQ(values1.size(), 1);
    EXPECT_EQ(*values1[0], 42);

    auto values2 = int_map.Get("key2");
    EXPECT_EQ(values2.size(), 1);
    EXPECT_EQ(*values2[0], 84);
}
