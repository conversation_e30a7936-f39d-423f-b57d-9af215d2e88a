// Copyright(C) 2023 InfiniFlow, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gtest/gtest.h"
import base_test;
import stl;
import third_party;
import secondary_index_data;
import data_type;
import logical_type;
import table_index_meeta;
import local_file_handle;
import file_system;
import file_system_type;

using namespace infinity;

class LowCardinalitySecondaryIndexTest : public BaseTest {
public:
    LowCardinalitySecondaryIndexTest() = default;
    ~LowCardinalitySecondaryIndexTest() = default;

    void SetUp() override {
        BaseTest::SetUp();
        // Create test data directory
        test_dir_ = "/var/infinity/tmp/low_cardinality_test";
        auto fs = MakeUnique<LocalFileSystem>();
        if (!fs->Exists(test_dir_)) {
            fs->CreateDirectory(test_dir_);
        }
    }

    void TearDown() override {
        // Clean up test files
        auto fs = MakeUnique<LocalFileSystem>();
        if (fs->Exists(test_dir_)) {
            fs->DeleteDirectory(test_dir_);
        }
        BaseTest::TearDown();
    }

protected:
    String test_dir_;

    // Helper function to create test data with low cardinality
    template<typename T>
    MultiMap<T, u32> CreateLowCardinalityData(u32 chunk_row_count, u32 unique_values = 5) {
        MultiMap<T, u32> test_data;
        
        // Create low cardinality data - many rows with same key values
        for (u32 i = 0; i < chunk_row_count; ++i) {
            T key = static_cast<T>(i % unique_values); // Only 'unique_values' distinct keys
            test_data.emplace(key, i);
        }
        
        return test_data;
    }

    // Helper function to verify range query results
    template<typename T>
    void VerifyRangeQuery(const MultiMap<T, u32>& original_data, 
                         const Vector<u32>& result_offsets,
                         T begin_val, T end_val) {
        Set<u32> expected_offsets;
        
        // Find expected offsets from original data
        for (const auto& [key, offset] : original_data) {
            if (key >= begin_val && key <= end_val) {
                expected_offsets.insert(offset);
            }
        }
        
        // Convert result to set for comparison
        Set<u32> actual_offsets(result_offsets.begin(), result_offsets.end());
        
        EXPECT_EQ(expected_offsets.size(), actual_offsets.size());
        EXPECT_EQ(expected_offsets, actual_offsets);
    }
};

TEST_F(LowCardinalitySecondaryIndexTest, TestIntegerLowCardinality) {
    const u32 chunk_row_count = 100;
    const u32 unique_values = 5;
    
    // Create test data
    auto test_data = CreateLowCardinalityData<i32>(chunk_row_count, unique_values);
    
    // Create low cardinality secondary index
    auto data_type = MakeShared<DataType>(LogicalType::kInteger);
    auto* index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);
    ASSERT_NE(index, nullptr);
    
    // Insert data
    index->InsertData(&test_data);
    
    // Verify unique key count
    EXPECT_EQ(index->GetUniqueKeyCount(), unique_values);
    
    // Test range queries
    const i32* unique_keys = static_cast<const i32*>(index->GetUniqueKeysPtr());
    ASSERT_NE(unique_keys, nullptr);
    
    // Verify unique keys are sorted
    for (u32 i = 1; i < unique_values; ++i) {
        EXPECT_LT(unique_keys[i-1], unique_keys[i]);
    }
    
    // Test getting offsets for specific keys
    for (u32 i = 0; i < unique_values; ++i) {
        i32 key = static_cast<i32>(i);
        const auto* bitmap = static_cast<const Bitmap*>(index->GetOffsetsForKeyPtr(&key));
        ASSERT_NE(bitmap, nullptr);
        
        // Count expected offsets for this key
        u32 expected_count = 0;
        for (const auto& [data_key, offset] : test_data) {
            if (data_key == key) {
                expected_count++;
            }
        }
        
        // Count actual offsets in bitmap
        u32 actual_count = 0;
        bitmap->RoaringBitmapApplyFunc([&actual_count](u32 offset) -> bool {
            actual_count++;
            return true;
        });
        
        EXPECT_EQ(expected_count, actual_count);
    }
    
    delete index;
}

TEST_F(LowCardinalitySecondaryIndexTest, TestSerializationDeserialization) {
    const u32 chunk_row_count = 50;
    const u32 unique_values = 3;
    
    // Create test data
    auto test_data = CreateLowCardinalityData<i64>(chunk_row_count, unique_values);
    
    // Create and populate index
    auto data_type = MakeShared<DataType>(LogicalType::kBigInt);
    auto* original_index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);
    original_index->InsertData(&test_data);
    
    // Serialize to file
    String test_file = test_dir_ + "/test_index.bin";
    auto fs = MakeUnique<LocalFileSystem>();
    auto file_handle = fs->OpenFile(test_file, FileFlags::WRITE_FLAG | FileFlags::CREATE_FLAG, FileLockType::kNoLock);
    original_index->SaveIndexInner(*file_handle);
    file_handle->Close();
    
    // Create new index and deserialize
    auto* new_index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);
    file_handle = fs->OpenFile(test_file, FileFlags::READ_flag, FileLockType::kNoLock);
    new_index->ReadIndexInner(*file_handle);
    file_handle->Close();
    
    // Verify deserialized index
    EXPECT_EQ(new_index->GetUniqueKeyCount(), unique_values);
    EXPECT_EQ(new_index->GetUniqueKeyCount(), original_index->GetUniqueKeyCount());
    
    // Compare unique keys
    const i64* original_keys = static_cast<const i64*>(original_index->GetUniqueKeysPtr());
    const i64* new_keys = static_cast<const i64*>(new_index->GetUniqueKeysPtr());
    
    for (u32 i = 0; i < unique_values; ++i) {
        EXPECT_EQ(original_keys[i], new_keys[i]);
    }
    
    // Compare bitmaps for each key
    for (u32 i = 0; i < unique_values; ++i) {
        i64 key = static_cast<i64>(i);
        const auto* original_bitmap = static_cast<const Bitmap*>(original_index->GetOffsetsForKeyPtr(&key));
        const auto* new_bitmap = static_cast<const Bitmap*>(new_index->GetOffsetsForKeyPtr(&key));
        
        Vector<u32> original_offsets, new_offsets;
        
        original_bitmap->RoaringBitmapApplyFunc([&original_offsets](u32 offset) -> bool {
            original_offsets.push_back(offset);
            return true;
        });
        
        new_bitmap->RoaringBitmapApplyFunc([&new_offsets](u32 offset) -> bool {
            new_offsets.push_back(offset);
            return true;
        });
        
        EXPECT_EQ(original_offsets, new_offsets);
    }
    
    delete original_index;
    delete new_index;
}

TEST_F(LowCardinalitySecondaryIndexTest, TestFloatLowCardinality) {
    const u32 chunk_row_count = 80;
    const u32 unique_values = 4;
    
    // Create test data with float keys
    MultiMap<float, u32> test_data;
    Vector<float> unique_float_keys = {1.5f, 2.5f, 3.5f, 4.5f};
    
    for (u32 i = 0; i < chunk_row_count; ++i) {
        float key = unique_float_keys[i % unique_values];
        test_data.emplace(key, i);
    }
    
    // Create low cardinality secondary index
    auto data_type = MakeShared<DataType>(LogicalType::kFloat);
    auto* index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);
    
    // Insert data
    index->InsertData(&test_data);
    
    // Verify unique key count
    EXPECT_EQ(index->GetUniqueKeyCount(), unique_values);
    
    // Test specific key lookup
    for (u32 i = 0; i < unique_values; ++i) {
        float key = unique_float_keys[i];
        const auto* bitmap = static_cast<const Bitmap*>(index->GetOffsetsForKeyPtr(&key));
        ASSERT_NE(bitmap, nullptr);
        
        // Verify offsets for this key
        Vector<u32> actual_offsets;
        bitmap->RoaringBitmapApplyFunc([&actual_offsets](u32 offset) -> bool {
            actual_offsets.push_back(offset);
            return true;
        });
        
        // Expected offsets for this key
        Vector<u32> expected_offsets;
        for (const auto& [data_key, offset] : test_data) {
            if (data_key == key) {
                expected_offsets.push_back(offset);
            }
        }
        
        std::sort(actual_offsets.begin(), actual_offsets.end());
        std::sort(expected_offsets.begin(), expected_offsets.end());
        EXPECT_EQ(expected_offsets, actual_offsets);
    }
    
    delete index;
}

TEST_F(LowCardinalitySecondaryIndexTest, TestVarcharLowCardinality) {
    const u32 chunk_row_count = 60;
    const u32 unique_values = 3;

    // Create test data with varchar keys
    MultiMap<VarcharT, u32> test_data;
    Vector<String> unique_strings = {"apple", "banana", "cherry"};

    for (u32 i = 0; i < chunk_row_count; ++i) {
        String key_str = unique_strings[i % unique_values];
        VarcharT key;
        key.InitializeAsValue(key_str);
        test_data.emplace(key, i);
    }

    // Create low cardinality secondary index
    auto data_type = MakeShared<DataType>(LogicalType::kVarchar);
    auto* index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);

    // Insert data
    index->InsertData(&test_data);

    // Verify unique key count
    EXPECT_EQ(index->GetUniqueKeyCount(), unique_values);

    delete index;
}

TEST_F(LowCardinalitySecondaryIndexTest, TestEmptyIndex) {
    const u32 chunk_row_count = 0;

    // Create empty test data
    MultiMap<i32, u32> test_data;

    // Create low cardinality secondary index
    auto data_type = MakeShared<DataType>(LogicalType::kInteger);
    auto* index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);

    // Insert empty data
    index->InsertData(&test_data);

    // Verify empty index
    EXPECT_EQ(index->GetUniqueKeyCount(), 0);
    EXPECT_EQ(index->GetUniqueKeysPtr(), nullptr);

    delete index;
}

TEST_F(LowCardinalitySecondaryIndexTest, TestSingleValueCardinality) {
    const u32 chunk_row_count = 50;
    const u32 unique_values = 1;

    // Create test data with only one unique value
    auto test_data = CreateLowCardinalityData<i16>(chunk_row_count, unique_values);

    // Create low cardinality secondary index
    auto data_type = MakeShared<DataType>(LogicalType::kSmallInt);
    auto* index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);

    // Insert data
    index->InsertData(&test_data);

    // Verify single unique key
    EXPECT_EQ(index->GetUniqueKeyCount(), 1);

    // Test the single key
    i16 key = 0;
    const auto* bitmap = static_cast<const Bitmap*>(index->GetOffsetsForKeyPtr(&key));
    ASSERT_NE(bitmap, nullptr);

    // All offsets should be in this single bitmap
    u32 offset_count = 0;
    bitmap->RoaringBitmapApplyFunc([&offset_count](u32 offset) -> bool {
        offset_count++;
        return true;
    });

    EXPECT_EQ(offset_count, chunk_row_count);

    delete index;
}

TEST_F(LowCardinalitySecondaryIndexTest, TestComparisonWithHighCardinality) {
    const u32 chunk_row_count = 100;
    const u32 unique_values = 5;

    // Create test data
    auto test_data = CreateLowCardinalityData<i32>(chunk_row_count, unique_values);

    // Create both high and low cardinality indexes
    auto data_type = MakeShared<DataType>(LogicalType::kInteger);
    auto* high_card_index = GetSecondaryIndexDataWithCardinality<HighCardinalityTag>(data_type, chunk_row_count, true);
    auto* low_card_index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);

    // Insert same data into both
    high_card_index->InsertData(&test_data);
    low_card_index->InsertData(&test_data);

    // High cardinality should have 0 unique keys (default implementation)
    EXPECT_EQ(high_card_index->GetUniqueKeyCount(), 0);
    EXPECT_EQ(high_card_index->GetUniqueKeysPtr(), nullptr);

    // Low cardinality should have the expected unique keys
    EXPECT_EQ(low_card_index->GetUniqueKeyCount(), unique_values);
    EXPECT_NE(low_card_index->GetUniqueKeysPtr(), nullptr);

    // Verify low cardinality index has correct data
    for (u32 i = 0; i < unique_values; ++i) {
        i32 key = static_cast<i32>(i);
        const auto* bitmap = static_cast<const Bitmap*>(low_card_index->GetOffsetsForKeyPtr(&key));
        ASSERT_NE(bitmap, nullptr);

        u32 expected_count = chunk_row_count / unique_values;
        u32 actual_count = 0;
        bitmap->RoaringBitmapApplyFunc([&actual_count](u32 offset) -> bool {
            actual_count++;
            return true;
        });

        EXPECT_EQ(expected_count, actual_count);
    }

    delete high_card_index;
    delete low_card_index;
}

// Test with different data types using typed test
template <typename T>
class LowCardinalityTypedTest : public LowCardinalitySecondaryIndexTest {
public:
    using ValueType = T;
};

using TestTypes = ::testing::Types<i8, i16, i32, i64, u8, u16, u32, u64, float, double>;
TYPED_TEST_SUITE(LowCardinalityTypedTest, TestTypes);

TYPED_TEST(LowCardinalityTypedTest, TestBasicFunctionality) {
    using ValueType = typename TestFixture::ValueType;
    const u32 chunk_row_count = 40;
    const u32 unique_values = 4;

    // Create test data
    MultiMap<ValueType, u32> test_data;
    for (u32 i = 0; i < chunk_row_count; ++i) {
        ValueType key = static_cast<ValueType>(i % unique_values);
        test_data.emplace(key, i);
    }

    // Determine the appropriate LogicalType
    LogicalType logical_type;
    if constexpr (std::is_same_v<ValueType, i8>) logical_type = LogicalType::kTinyInt;
    else if constexpr (std::is_same_v<ValueType, i16>) logical_type = LogicalType::kSmallInt;
    else if constexpr (std::is_same_v<ValueType, i32>) logical_type = LogicalType::kInteger;
    else if constexpr (std::is_same_v<ValueType, i64>) logical_type = LogicalType::kBigInt;
    else if constexpr (std::is_same_v<ValueType, u8>) logical_type = LogicalType::kTinyInt;
    else if constexpr (std::is_same_v<ValueType, u16>) logical_type = LogicalType::kSmallInt;
    else if constexpr (std::is_same_v<ValueType, u32>) logical_type = LogicalType::kInteger;
    else if constexpr (std::is_same_v<ValueType, u64>) logical_type = LogicalType::kBigInt;
    else if constexpr (std::is_same_v<ValueType, float>) logical_type = LogicalType::kFloat;
    else if constexpr (std::is_same_v<ValueType, double>) logical_type = LogicalType::kDouble;

    // Create low cardinality secondary index
    auto data_type = MakeShared<DataType>(logical_type);
    auto* index = GetSecondaryIndexDataWithCardinality<LowCardinalityTag>(data_type, chunk_row_count, true);

    // Insert data
    index->InsertData(&test_data);

    // Verify basic properties
    EXPECT_EQ(index->GetUniqueKeyCount(), unique_values);
    EXPECT_NE(index->GetUniqueKeysPtr(), nullptr);

    // Test each unique key
    for (u32 i = 0; i < unique_values; ++i) {
        ValueType key = static_cast<ValueType>(i);
        const auto* bitmap = static_cast<const Bitmap*>(index->GetOffsetsForKeyPtr(&key));
        ASSERT_NE(bitmap, nullptr);

        // Verify bitmap contains correct offsets
        Vector<u32> actual_offsets;
        bitmap->RoaringBitmapApplyFunc([&actual_offsets](u32 offset) -> bool {
            actual_offsets.push_back(offset);
            return true;
        });

        EXPECT_EQ(actual_offsets.size(), chunk_row_count / unique_values);
    }

    delete index;
}

TEST_F(LowCardinalitySecondaryIndexTest, TestFactoryWithTableIndexMeeta) {
    const u32 chunk_row_count = 30;

    // Create test data
    auto test_data = CreateLowCardinalityData<i32>(chunk_row_count, 3);

    // Create TableIndexMeeta and set cardinality
    // Note: This is a simplified test - in real usage TableIndexMeeta would be properly initialized
    // For now we test the factory function with nullptr (should default to HighCardinality)
    auto data_type = MakeShared<DataType>(LogicalType::kInteger);

    // Test with nullptr TableIndexMeeta (should default to HighCardinality)
    void* index_ptr = GetSecondaryIndexDataWithMeeta(data_type, chunk_row_count, true, nullptr);
    ASSERT_NE(index_ptr, nullptr);

    // Cast to base type and verify it's HighCardinality (should have 0 unique keys)
    auto* index = static_cast<SecondaryIndexDataBase<HighCardinalityTag>*>(index_ptr);
    index->InsertData(&test_data);
    EXPECT_EQ(index->GetUniqueKeyCount(), 0); // HighCardinality default behavior

    delete index;
}
