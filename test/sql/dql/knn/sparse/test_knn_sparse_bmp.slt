statement ok
DROP TABLE IF EXISTS test_knn_sparse_bmp;

statement ok
CREATE TABLE test_knn_sparse_bmp (col1 INT, col2 SPARSE(FLOAT,100));

statement ok
COPY test_knn_sparse_bmp FROM '/var/infinity/test_data/sparse_knn.csv' WITH (FORMAT CSV);

statement ok
CREATE INDEX idx1 ON test_knn_sparse_bmp (col2) USING Bmp WITH (block_size = 8, compress_type = compress);

statement ok
CREATE INDEX idx2 ON test_knn_sparse_bmp (col2) USING Bmp WITH (block_size = 16, compress_type = raww);

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3) USING INDEX(idx1);
----
4
2
1

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3) USING INDEX(idx2);
----
4
2
1

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 10);
----
4
2
1
3
5

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 10) USING INDEX(idx1);
----
4
2
1
3
5

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 10) USING INDEX(idx2);
----
4
2
1
3
5

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3);
----
4
2
1

statement ok
OPTIMIZE idx1 ON test_knn_sparse_bmp WITH (topk = 3);

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3);
----
4
2
1

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3) WITH (alpha = 1.0, beta = 1.0, tail = T);
----
4
2
1

statement ok
COPY test_knn_sparse_bmp FROM '/var/infinity/test_data/sparse_knn.csv' WITH (FORMAT CSV);

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3);
----
4
4
2

statement ok
COPY test_knn_sparse_bmp FROM '/var/infinity/test_data/sparse_knn.csv' WITH (FORMAT CSV);

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3);
----
4
4
4

statement ok
OPTIMIZE idx1 ON test_knn_sparse_bmp WITH (topk = 3);

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3) USING INDEX(idx1);
----
4
4
4

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3) USING INDEX(idx2);
----
4
4
4

query I
SELECT col1 FROM test_knn_sparse_bmp SEARCH MATCH SPARSE (col2, [0:1.0,20:2.0,80:3.0], 'ip', 3);
----
4
4
4

statement ok
DROP TABLE test_knn_sparse_bmp;
