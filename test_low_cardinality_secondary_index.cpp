// Simple test to verify low cardinality secondary index implementation
#include <iostream>
#include <vector>
#include <map>

// Mock types for testing
using u32 = uint32_t;
using i32 = int32_t;

template<typename T>
using Vector = std::vector<T>;

template<typename K, typename V>
using Map = std::map<K, V>;

template<typename K, typename V>
using MultiMap = std::multimap<K, V>;

// Mock RoaringBitmap implementation for testing
class MockBitmap {
private:
    std::vector<u32> offsets_;
    u32 count_;
    
public:
    MockBitmap(u32 count) : count_(count) {}
    
    void SetTrue(u32 offset) {
        if (std::find(offsets_.begin(), offsets_.end(), offset) == offsets_.end()) {
            offsets_.push_back(offset);
        }
    }
    
    template<typename Func>
    void RoaringBitmapApplyFunc(Func&& func) const {
        for (u32 offset : offsets_) {
            if (!func(offset)) break;
        }
    }
    
    i32 GetSizeInBytes() const { return sizeof(u32) * offsets_.size(); }
    
    void WriteAdv(char*& ptr) const {
        // Mock serialization
        for (u32 offset : offsets_) {
            *reinterpret_cast<u32*>(ptr) = offset;
            ptr += sizeof(u32);
        }
    }
    
    void ReadAdv(const char*& ptr) {
        // Mock deserialization - would need size info in real implementation
    }
};

using Bitmap = MockBitmap;

// Test the low cardinality approach
void testLowCardinalitySecondaryIndex() {
    std::cout << "Testing Low Cardinality Secondary Index Implementation\n";
    
    // Simulate data: key -> list of offsets
    // For low cardinality, we expect many duplicate keys
    MultiMap<i32, u32> test_data = {
        {10, 0}, {10, 5}, {10, 12},  // key 10 appears at offsets 0, 5, 12
        {20, 1}, {20, 8}, {20, 15},  // key 20 appears at offsets 1, 8, 15
        {30, 3}, {30, 9},            // key 30 appears at offsets 3, 9
        {40, 7}                      // key 40 appears at offset 7
    };
    
    const u32 chunk_row_count = 16;
    
    // Build unique keys and bitmaps (simulating the low cardinality implementation)
    Map<i32, Vector<u32>> key_to_offsets;
    for (const auto& [key, offset] : test_data) {
        key_to_offsets[key].push_back(offset);
    }
    
    Vector<i32> unique_keys;
    Vector<Bitmap> offset_bitmaps;
    
    for (const auto& [key, offsets] : key_to_offsets) {
        unique_keys.push_back(key);
        
        Bitmap bitmap(chunk_row_count);
        for (u32 offset : offsets) {
            bitmap.SetTrue(offset);
        }
        offset_bitmaps.emplace_back(std::move(bitmap));
    }
    
    std::cout << "Unique keys count: " << unique_keys.size() << "\n";
    std::cout << "Original data size: " << test_data.size() << "\n";
    std::cout << "Space savings: " << (test_data.size() - unique_keys.size()) << " entries\n";
    
    // Test range query [15, 25] - should match keys 20
    i32 begin_val = 15, end_val = 25;
    auto begin_it = std::lower_bound(unique_keys.begin(), unique_keys.end(), begin_val);
    auto end_it = std::upper_bound(unique_keys.begin(), unique_keys.end(), end_val);
    
    std::cout << "\nRange query [" << begin_val << ", " << end_val << "]:\n";
    Vector<u32> result_offsets;
    
    for (auto it = begin_it; it != end_it; ++it) {
        size_t index = it - unique_keys.begin();
        std::cout << "Found key: " << *it << " with offsets: ";
        
        offset_bitmaps[index].RoaringBitmapApplyFunc([&](u32 offset) -> bool {
            result_offsets.push_back(offset);
            std::cout << offset << " ";
            return true;
        });
        std::cout << "\n";
    }
    
    std::cout << "Total matching offsets: " << result_offsets.size() << "\n";
}

int main() {
    testLowCardinalitySecondaryIndex();
    return 0;
}
